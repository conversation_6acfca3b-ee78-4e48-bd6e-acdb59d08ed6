import { createMap, forMember, mapFrom, Mapper } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { VehicleDomain } from '../../domain/vehicle';
import { CreateVehicleDto } from '../../../../vehicle/vehicles/dto/create-vehicle.dto';
import { GetVehicleDto } from '../../../../vehicle/vehicles/dto/get-vehicle.dto';
import { VehicleEntity } from '../../../../vehicle/vehicles/infrastructure/entities/vehicle.entity';

@Injectable()
export class VehicleProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      createMap(
        mapper,
        CreateVehicleDto,
        VehicleDomain,
        forMember(
          (dest) => dest.packageType,
          mapFrom((src) => src.packageType),
        ),
      );

      createMap(
        mapper,
        VehicleDomain,
        VehicleEntity,
        forMember(
          (dest) => dest.packageType,
          mapFrom((src) => src.packageType),
        ),
      );

      createMap(
        mapper,
        VehicleEntity,
        VehicleDomain,
        forMember(
          (dest) => dest.packageType,
          mapFrom((src) => src.packageType),
        ),
      );

      createMap(
        mapper,
        VehicleDomain,
        GetVehicleDto,
        forMember(
          (dest) => dest.packageType,
          mapFrom((src) => src.packageType),
        ),
        forMember(
          (dest) => dest.vehicleType,
          mapFrom((src) => src.vehicleType?.name),
        ),
      );
    };
  }
}
